# 数据资产存证上链记录查询问题修复

## 问题描述

在 `CertificateInfoAutoChainServiceImpl` 中，`isCertificateOnChain` 方法只查询了 `data_certificate_chain_record` 表来判断是否已上链，但实际的上链记录是存储在 `business_blockchain_link` 表中的。

## 问题分析

1. **上链记录存储位置不一致**：
   - `Copyright2Sop.issueNft` 方法会在 `business_blockchain_link` 表中创建记录
   - 但 `isCertificateOnChain` 方法只查询 `data_certificate_chain_record` 表

2. **表结构对比**：
   - `business_blockchain_link` 表：实际的区块链上链记录，包含完整的区块链信息
   - `data_certificate_chain_record` 表：本地的上链状态记录，主要用于跟踪NFT发行状态

## 修复方案

修改 `CertificateInfoAutoChainServiceImpl.isCertificateOnChain` 方法，使其同时查询两个表：

1. **优先查询 `business_blockchain_link` 表**：这是实际的上链记录存储位置
2. **兼容查询 `data_certificate_chain_record` 表**：保持向后兼容性
3. **逻辑判断**：只要任一表中有记录，就认为已上链

## 修改内容

### 1. 添加依赖注入

```java
@Resource
private BusinessBlockchainLinkService businessBlockchainLinkService;
```

### 2. 修改查询逻辑

```java
@Override
public boolean isCertificateOnChain(Integer certificateInfoId) {
    // 优先查询 business_blockchain_link 表中的上链记录
    // 这是实际的上链记录存储位置
    var blockchainLinks = businessBlockchainLinkService.getBlockchainLinksByBusinessIdAndType(
            certificateInfoId.longValue(), 
            BusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE.getCode()
    );
    
    boolean hasBlockchainRecord = !blockchainLinks.isEmpty();
    
    // 同时检查本地的上链记录表（用于兼容性）
    CertificateChainRecordDO successRecord = certificateChainRecordMapper
            .selectSuccessByCertificateInfoId(certificateInfoId);
    
    boolean hasLocalRecord = successRecord != null;
    
    // 只要有任一记录存在，就认为已上链
    boolean isOnChain = hasBlockchainRecord || hasLocalRecord;
    
    log.debug("检查数据资产是否已上链，存证ID: {}, business_blockchain_link记录: {}, 本地记录: {}, 最终结果: {}", 
            certificateInfoId, hasBlockchainRecord, hasLocalRecord, isOnChain);

    return isOnChain;
}
```

## 测试验证

创建了完整的单元测试 `CertificateInfoAutoChainServiceImplTest`，覆盖以下场景：

1. **只有区块链记录**：应该返回 true
2. **只有本地记录**：应该返回 true  
3. **没有任何记录**：应该返回 false
4. **同时有两种记录**：应该返回 true

## 影响范围

- **向后兼容**：保持了对原有 `data_certificate_chain_record` 表的查询
- **功能增强**：新增了对 `business_blockchain_link` 表的查询
- **性能影响**：增加了一次数据库查询，但提高了查询准确性

## 相关文件

- `yudao-module-business/src/main/java/cn/iocoder/yudao/module/business/service/certificateinfo/impl/CertificateInfoAutoChainServiceImpl.java`
- `yudao-module-business/src/test/java/cn/iocoder/yudao/module/business/service/certificateinfo/CertificateInfoAutoChainServiceImplTest.java`

## 建议

1. **数据一致性**：建议在后续版本中统一上链记录的存储策略
2. **性能优化**：可以考虑将两个查询合并为一个联合查询
3. **监控告警**：建议添加监控，当两个表的数据不一致时发出告警
