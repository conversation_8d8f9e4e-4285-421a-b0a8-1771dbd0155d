package cn.iocoder.yudao.module.business.service.certificateinfo;

import cn.iocoder.yudao.module.business.dal.dataobject.certificateinfo.CertificateChainRecordDO;
import cn.iocoder.yudao.module.business.dal.mysql.certificateinfo.CertificateChainRecordMapper;
import cn.iocoder.yudao.module.business.enums.BusinessTypeEnum;
import cn.iocoder.yudao.module.business.service.blockchainlink.BusinessBlockchainLinkService;
import cn.iocoder.yudao.module.business.service.certificateinfo.impl.CertificateInfoAutoChainServiceImpl;
import cn.iocoder.yudao.module.extend.entity.copyright.BusinessBlockchainLinkDO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 数据资产存证自动上链服务测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class CertificateInfoAutoChainServiceImplTest {

    @Mock
    private BusinessBlockchainLinkService businessBlockchainLinkService;

    @Mock
    private CertificateChainRecordMapper certificateChainRecordMapper;

    @InjectMocks
    private CertificateInfoAutoChainServiceImpl certificateInfoAutoChainService;

    @Test
    void testIsCertificateOnChain_HasBlockchainRecord() {
        // 准备测试数据
        Integer certificateInfoId = 123;
        BusinessBlockchainLinkDO blockchainLink = new BusinessBlockchainLinkDO();
        blockchainLink.setId(1L);
        blockchainLink.setBusinessId(certificateInfoId.longValue());
        blockchainLink.setBusinessType(BusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE.getCode());

        // Mock 方法调用
        when(businessBlockchainLinkService.getBlockchainLinksByBusinessIdAndType(
                certificateInfoId.longValue(),
                BusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE.getCode()
        )).thenReturn(List.of(blockchainLink));

        when(certificateChainRecordMapper.selectSuccessByCertificateInfoId(certificateInfoId))
                .thenReturn(null);

        // 执行测试
        boolean result = certificateInfoAutoChainService.isCertificateOnChain(certificateInfoId);

        // 验证结果
        assertTrue(result, "应该返回true，因为存在区块链记录");

        // 验证方法调用
        verify(businessBlockchainLinkService).getBlockchainLinksByBusinessIdAndType(
                certificateInfoId.longValue(),
                BusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE.getCode()
        );
        verify(certificateChainRecordMapper).selectSuccessByCertificateInfoId(certificateInfoId);
    }

    @Test
    void testIsCertificateOnChain_HasLocalRecord() {
        // 准备测试数据
        Integer certificateInfoId = 123;
        CertificateChainRecordDO chainRecord = new CertificateChainRecordDO();
        chainRecord.setId(1L);
        chainRecord.setCertificateInfoId(certificateInfoId);
        chainRecord.setNftIssueStatus("S");

        // Mock 方法调用
        when(businessBlockchainLinkService.getBlockchainLinksByBusinessIdAndType(
                certificateInfoId.longValue(),
                BusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE.getCode()
        )).thenReturn(Collections.emptyList());

        when(certificateChainRecordMapper.selectSuccessByCertificateInfoId(certificateInfoId))
                .thenReturn(chainRecord);

        // 执行测试
        boolean result = certificateInfoAutoChainService.isCertificateOnChain(certificateInfoId);

        // 验证结果
        assertTrue(result, "应该返回true，因为存在本地记录");

        // 验证方法调用
        verify(businessBlockchainLinkService).getBlockchainLinksByBusinessIdAndType(
                certificateInfoId.longValue(),
                BusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE.getCode()
        );
        verify(certificateChainRecordMapper).selectSuccessByCertificateInfoId(certificateInfoId);
    }

    @Test
    void testIsCertificateOnChain_NoRecord() {
        // 准备测试数据
        Integer certificateInfoId = 123;

        // Mock 方法调用
        when(businessBlockchainLinkService.getBlockchainLinksByBusinessIdAndType(
                certificateInfoId.longValue(),
                BusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE.getCode()
        )).thenReturn(Collections.emptyList());

        when(certificateChainRecordMapper.selectSuccessByCertificateInfoId(certificateInfoId))
                .thenReturn(null);

        // 执行测试
        boolean result = certificateInfoAutoChainService.isCertificateOnChain(certificateInfoId);

        // 验证结果
        assertFalse(result, "应该返回false，因为没有任何记录");

        // 验证方法调用
        verify(businessBlockchainLinkService).getBlockchainLinksByBusinessIdAndType(
                certificateInfoId.longValue(),
                BusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE.getCode()
        );
        verify(certificateChainRecordMapper).selectSuccessByCertificateInfoId(certificateInfoId);
    }

    @Test
    void testIsCertificateOnChain_HasBothRecords() {
        // 准备测试数据
        Integer certificateInfoId = 123;
        
        BusinessBlockchainLinkDO blockchainLink = new BusinessBlockchainLinkDO();
        blockchainLink.setId(1L);
        blockchainLink.setBusinessId(certificateInfoId.longValue());
        blockchainLink.setBusinessType(BusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE.getCode());

        CertificateChainRecordDO chainRecord = new CertificateChainRecordDO();
        chainRecord.setId(1L);
        chainRecord.setCertificateInfoId(certificateInfoId);
        chainRecord.setNftIssueStatus("S");

        // Mock 方法调用
        when(businessBlockchainLinkService.getBlockchainLinksByBusinessIdAndType(
                certificateInfoId.longValue(),
                BusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE.getCode()
        )).thenReturn(List.of(blockchainLink));

        when(certificateChainRecordMapper.selectSuccessByCertificateInfoId(certificateInfoId))
                .thenReturn(chainRecord);

        // 执行测试
        boolean result = certificateInfoAutoChainService.isCertificateOnChain(certificateInfoId);

        // 验证结果
        assertTrue(result, "应该返回true，因为同时存在区块链记录和本地记录");

        // 验证方法调用
        verify(businessBlockchainLinkService).getBlockchainLinksByBusinessIdAndType(
                certificateInfoId.longValue(),
                BusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE.getCode()
        );
        verify(certificateChainRecordMapper).selectSuccessByCertificateInfoId(certificateInfoId);
    }
}
